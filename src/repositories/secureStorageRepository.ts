/**
 * Secure Storage Repository
 *
 * Data access layer for secure storage operations.
 * Provides low-level storage operations and platform abstraction.
 *
 * 🔄 SHARED BETWEEN UNA AND OKTA IMPLEMENTATIONS
 * - Used by both UNA authentication and Okta POC
 * - Storage keys are namespaced to prevent conflicts
 * - Safe to enhance, but maintain backward compatibility
 * - UNA/Okta split for PoC support; when connecting with middleware, will probably just have the combined
 */

import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import type { IStorageRepository } from '@/types';

// In-memory storage for web to avoid persisting sensitive tokens
const memoryStorage = new Map<string, string>();

/**
 * User authentication data interface for Okta OAuth tokens
 */
export interface AuthData {
	/** Access token for API calls */
	token: string;
	/** Refresh token for token renewal */
	refreshToken: string;
	/** ID token containing user identity information */
	idToken?: string;
	/** Token expiration timestamp (milliseconds since epoch) */
	expiresAt: number;
	/** Token type (typically 'Bearer') */
	tokenType?: string;
	/** OAuth scopes granted */
	scope?: string;
	/** User profile information from ID token */
	userProfile?: {
		sub: string;
		email?: string;
		name?: string;
		preferred_username?: string;
		given_name?: string;
		family_name?: string;
	};
}

/**
 * Storage keys constants (UNA/general app)
 * Prefixed with 'una_' to avoid collisions with Okta-specific storage
 */
export const StorageKeys = {
	AUTH_DATA: 'una_auth_data',
	USER_PROFILE: 'una_user_profile',
} as const;

/**
 * Repository for secure storage data access operations
 */
export class SecureStorageRepository implements IStorageRepository {
	private isSecureStoreAvailable: boolean;

	constructor() {
		// SecureStore is available on iOS and Android, but not on web
		this.isSecureStoreAvailable = Platform.OS !== 'web';
	}

	/**
	 * Store a value securely
	 */
	async setItem(key: string, value: string): Promise<void> {
		try {
			if (this.isSecureStoreAvailable) {
				await SecureStore.setItemAsync(key, value);
			} else {
				// Use in-memory storage on web to avoid persisting tokens
				memoryStorage.set(key, value);
				if (typeof __DEV__ !== 'undefined' && __DEV__) {
					console.warn(
						`[SecureStorageRepository] Using in-memory storage on web for key: ${key}`,
					);
				}
			}
		} catch (error) {
			console.error(`Failed to store item with key ${key}:`, error);
			throw new Error(`Storage operation failed: ${error}`);
		}
	}

	/**
	 * Retrieve a value securely
	 */
	async getItem(key: string): Promise<string | null> {
		try {
			if (this.isSecureStoreAvailable) {
				return await SecureStore.getItemAsync(key);
			} else {
				// Use in-memory storage on web
				return memoryStorage.get(key) ?? null;
			}
		} catch (error) {
			console.error(`Failed to retrieve item with key ${key}:`, error);
			return null;
		}
	}

	/**
	 * Remove a value securely
	 */
	async removeItem(key: string): Promise<void> {
		try {
			if (this.isSecureStoreAvailable) {
				await SecureStore.deleteItemAsync(key);
			} else {
				// Use in-memory storage on web
				memoryStorage.delete(key);
			}
		} catch (error) {
			console.error(`Failed to remove item with key ${key}:`, error);
			throw new Error(`Storage operation failed: ${error}`);
		}
	}

	/**
	 * Check if a key exists
	 */
	async hasItem(key: string): Promise<boolean> {
		try {
			const value = await this.getItem(key);
			return value !== null;
		} catch (error) {
			console.error(`Failed to check item with key ${key}:`, error);
			return false;
		}
	}

	/**
	 * Store JSON data securely
	 */
	async setObject<T>(key: string, value: T): Promise<void> {
		try {
			const jsonString = JSON.stringify(value);
			await this.setItem(key, jsonString);
		} catch (error) {
			console.error(`Failed to store object with key ${key}:`, error);
			throw new Error(`Object storage operation failed: ${error}`);
		}
	}

	/**
	 * Retrieve JSON data securely
	 */
	async getObject<T>(key: string): Promise<T | null> {
		try {
			const jsonString = await this.getItem(key);
			if (jsonString === null) {
				return null;
			}
			return JSON.parse(jsonString) as T;
		} catch (error) {
			console.error(`Failed to retrieve object with key ${key}:`, error);
			return null;
		}
	}

	/**
	 * Remove JSON data securely
	 */
	async removeObject(key: string): Promise<void> {
		try {
			await this.removeItem(key);
		} catch (error) {
			console.error(`Failed to remove object with key ${key}:`, error);
			throw new Error(`Object removal operation failed: ${error}`);
		}
	}

	/**
	 * Store authentication data (data access only)
	 */
	async saveAuthData(authData: AuthData): Promise<void> {
		try {
			await this.setObject(StorageKeys.AUTH_DATA, authData);
		} catch (error) {
			throw new Error(`Failed to save authentication data: ${error}`);
		}
	}

	/**
	 * Retrieve authentication data (data access only)
	 */
	async getAuthData(): Promise<AuthData | null> {
		try {
			const authData = await this.getObject<AuthData>(StorageKeys.AUTH_DATA);
			return authData;
		} catch (error) {
			console.error('Failed to retrieve authentication data:', error);
			return null;
		}
	}

	/**
	 * Clear authentication data (data access only)
	 */
	async clearAuthData(): Promise<void> {
		try {
			await this.removeItem(StorageKeys.AUTH_DATA);
		} catch (error) {
			throw new Error(`Failed to clear authentication data: ${error}`);
		}
	}

	/**
	 * Clear all user data (data access only)
	 */
	async clearAllUserData(): Promise<void> {
		try {
			await this.clearAuthData();
		} catch (error) {
			throw new Error(`Failed to clear all user data: ${error}`);
		}
	}
}

// Export singleton instance
export const secureStorageRepository = new SecureStorageRepository();
