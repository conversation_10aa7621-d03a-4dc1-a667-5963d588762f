import React from 'react';
import { View, TextInput, Alert, ScrollView, StyleSheet } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import CustomButton from '@/components/ui/CustomButton';
import { useTheme } from '@/theme';
import { useApiConfiguration } from '@/hooks/useApiConfiguration';
import { useApiConfigurationTesting } from '@/hooks/__dev__/useApiConfigurationTesting';

// __DEV__ is a global variable in React Native
declare const __DEV__: boolean;

/// DEV-ONLY; Clean Architecture implementation; screen to test/show API key functionality
export default function ApiKeysScreen() {
	// Use production hook for basic configuration management
	const {
		// State
		apiKey,
		apiBaseUrl,
		storedConfig,
		isLoading: configLoading,
		// Actions
		setApiKey,
		setApiBaseUrl,
		saveApiKey,
		saveApiBaseUrl,
		deleteConfiguration,
		refreshStoredConfig,
	} = useApiConfiguration();

	// Use development hook for testing functionality
	const {
		// Test State
		testResults,
		apiTestResponse,
		isLoading: testLoading,
		// Test Actions
		importDevConfig,
		testRetrieveKey,
		testRetrieveUrl,
		testKeyExists,
		testApiCall,
		testFactoryPattern,
		runAllTests,
		// Utilities
		getTestStatusIcon,
	} = useApiConfigurationTesting();

	// Combined loading state
	const isLoading = configLoading || testLoading;

	const textColor = useTheme().colors.text;
	const borderColor = useTheme().colors.border;

	return (
		<ThemedView style={styles.container}>
			<ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
				<ThemedText style={styles.title}>API Configuration</ThemedText>
				<ThemedText style={styles.subtitle}>
					Manage API keys and base URLs with secure storage
				</ThemedText>

				{/* API Key Input Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Enter API Key</ThemedText>
					<TextInput
						style={[styles.input, { borderColor, color: textColor }]}
						value={apiKey}
						onChangeText={setApiKey}
						placeholder='Enter your API key here...'
						placeholderTextColor={borderColor}
						secureTextEntry={true}
						multiline={false}
					/>
					<CustomButton
						variant='primary'
						onPress={saveApiKey}
						disabled={isLoading}
						style={styles.button}
					>
						{isLoading ? 'Saving...' : 'Save API Key Securely'}
					</CustomButton>
				</View>

				{/* API Base URL Input Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Enter API Base URL</ThemedText>
					<TextInput
						style={[styles.input, { borderColor, color: textColor }]}
						value={apiBaseUrl}
						onChangeText={setApiBaseUrl}
						placeholder='https://api.example.com'
						placeholderTextColor={borderColor}
						secureTextEntry={false}
						multiline={false}
						keyboardType='url'
						autoCapitalize='none'
						autoCorrect={false}
					/>
					<CustomButton
						variant='primary'
						onPress={saveApiBaseUrl}
						disabled={isLoading}
						style={styles.button}
					>
						{isLoading ? 'Saving...' : 'Save API Base URL Securely'}
					</CustomButton>
				</View>

				{/* Dev Config Section - Only show in development */}
				{__DEV__ && (
					<View style={styles.section}>
						<ThemedText style={styles.sectionTitle}>🔧 Development Tools</ThemedText>
						<ThemedText style={styles.subtitle}>
							Auto-load configuration from dev.config.json
						</ThemedText>
						<CustomButton
							variant='secondary'
							onPress={async () => {
								await importDevConfig(true);
								await refreshStoredConfig();
							}}
							disabled={isLoading}
							style={styles.button}
						>
							{isLoading ? 'Loading...' : 'Load Dev Config'}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={() => {
								console.log('🔧 [iOS Debug] Manual debug info:');
								console.log('- __DEV__:', __DEV__);
								console.log('- Current stored config:', storedConfig);
								console.log('- Form API Key:', apiKey ? '[PRESENT]' : '[EMPTY]');
								console.log('- Form API URL:', apiBaseUrl || '[EMPTY]');
								Alert.alert('Debug Info', 'Check console for debug information', [
									{ text: 'OK' },
								]);
							}}
							disabled={isLoading}
							style={[styles.button, { marginTop: 8 }]}
						>
							🐛 Debug Info
						</CustomButton>
					</View>
				)}

				{/* Current Status Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Current Status</ThemedText>
					<View style={styles.statusContainer}>
						<ThemedText style={styles.statusText}>
							Stored API Key:{' '}
							{storedConfig.apiKey
								? `${storedConfig.apiKey.substring(0, 10)}...`
								: 'None'}
						</ThemedText>
						<ThemedText style={styles.statusText}>
							Stored API Base URL:{' '}
							{storedConfig.apiBaseUrl ? storedConfig.apiBaseUrl : 'None'}
						</ThemedText>
						<ThemedText style={styles.statusText}>Storage Available: ✅ Yes</ThemedText>
					</View>
				</View>

				{/* Test Operations Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Test Operations</ThemedText>

					<View style={styles.testGrid}>
						<CustomButton
							variant='secondary'
							onPress={() => testRetrieveKey(storedConfig.apiKey)}
							disabled={isLoading || !storedConfig.apiKey}
							style={styles.testButton}
						>
							Test Retrieve Key {getTestStatusIcon(testResults.retrieve)}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={() => testRetrieveUrl(storedConfig.apiBaseUrl)}
							disabled={isLoading || !storedConfig.apiBaseUrl}
							style={styles.testButton}
						>
							Test Retrieve URL {getTestStatusIcon(testResults.retrieveUrl)}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={() => testKeyExists(storedConfig.apiKey !== null)}
							disabled={isLoading}
							style={styles.testButton}
						>
							Test Exists {getTestStatusIcon(testResults.exists)}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={deleteConfiguration}
							disabled={
								isLoading || (!storedConfig.apiKey && !storedConfig.apiBaseUrl)
							}
							style={styles.testButton}
						>
							Delete All {getTestStatusIcon(testResults.delete)}
						</CustomButton>

						<CustomButton
							variant='primary'
							onPress={() =>
								testApiCall(storedConfig.apiKey!, storedConfig.apiBaseUrl!)
							}
							disabled={isLoading || !storedConfig.apiKey || !storedConfig.apiBaseUrl}
							style={styles.testButton}
						>
							Test API Call {getTestStatusIcon(testResults.apiTest)}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={() =>
								testFactoryPattern(storedConfig.apiKey!, storedConfig.apiBaseUrl!)
							}
							disabled={isLoading || !storedConfig.apiKey || !storedConfig.apiBaseUrl}
							style={styles.testButton}
						>
							Demo API Factory {getTestStatusIcon(testResults.apiTest)}
						</CustomButton>

						<CustomButton
							variant='primary'
							onPress={() =>
								runAllTests(storedConfig.apiKey, storedConfig.apiBaseUrl)
							}
							disabled={isLoading || !storedConfig.apiKey}
							style={[styles.testButton, styles.fullWidthButton]}
						>
							Run All Tests
						</CustomButton>
					</View>
				</View>

				{/* Test Results Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Test Results</ThemedText>
					<View style={styles.resultsContainer}>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Save API Key:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.save)}{' '}
								{testResults.save === null
									? 'Not tested'
									: testResults.save
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Save API URL:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.saveUrl)}{' '}
								{testResults.saveUrl === null
									? 'Not tested'
									: testResults.saveUrl
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Retrieve API Key:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.retrieve)}{' '}
								{testResults.retrieve === null
									? 'Not tested'
									: testResults.retrieve
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Retrieve API URL:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.retrieveUrl)}{' '}
								{testResults.retrieveUrl === null
									? 'Not tested'
									: testResults.retrieveUrl
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Exists Check:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.exists)}{' '}
								{testResults.exists === null
									? 'Not tested'
									: testResults.exists
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>API Test:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.apiTest)}{' '}
								{testResults.apiTest === null
									? 'Not tested'
									: testResults.apiTest
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Delete Operation:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.delete)}{' '}
								{testResults.delete === null
									? 'Not tested'
									: testResults.delete
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
					</View>
				</View>

				{/* API Response Section */}
				{apiTestResponse ? (
					<View style={styles.section}>
						<ThemedText style={styles.sectionTitle}>API Test Response</ThemedText>
						<View style={styles.responseContainer}>
							<ScrollView
								style={styles.responseScrollView}
								showsVerticalScrollIndicator={true}
								nestedScrollEnabled={true}
							>
								<ThemedText style={styles.responseText}>
									{apiTestResponse}
								</ThemedText>
							</ScrollView>
						</View>
					</View>
				) : null}
			</ScrollView>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
	scrollView: {
		flex: 1,
		padding: 20,
	},
	title: {
		fontSize: 28,
		fontWeight: 'bold',
		marginBottom: 8,
		textAlign: 'center',
	},
	subtitle: {
		fontSize: 16,
		opacity: 0.7,
		marginBottom: 30,
		textAlign: 'center',
	},
	section: {
		marginBottom: 30,
	},
	sectionTitle: {
		fontSize: 20,
		fontWeight: '600',
		marginBottom: 15,
	},
	input: {
		borderWidth: 1,
		borderRadius: 8,
		padding: 12,
		fontSize: 16,
		marginBottom: 15,
		minHeight: 50,
	},
	button: {
		marginVertical: 5,
	},
	statusContainer: {
		padding: 15,
		borderRadius: 8,
		backgroundColor: 'rgba(0, 0, 0, 0.05)',
	},
	statusText: {
		fontSize: 16,
		marginBottom: 5,
	},
	testGrid: {
		flexDirection: 'row',
		flexWrap: 'wrap',
		justifyContent: 'space-between',
	},
	testButton: {
		width: '48%',
		marginBottom: 10,
	},
	fullWidthButton: {
		width: '100%',
	},
	resultsContainer: {
		padding: 15,
		borderRadius: 8,
		backgroundColor: 'rgba(0, 0, 0, 0.05)',
	},
	resultRow: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		marginBottom: 8,
	},
	resultLabel: {
		fontSize: 16,
		flex: 1,
	},
	resultValue: {
		fontSize: 16,
		fontWeight: '500',
	},
	responseContainer: {
		backgroundColor: 'rgba(0, 0, 0, 0.05)',
		borderRadius: 8,
		padding: 15,
		maxHeight: 300,
	},
	responseScrollView: {
		maxHeight: 270,
	},
	responseText: {
		fontSize: 12,
		fontFamily: 'monospace',
		lineHeight: 16,
	},
});
