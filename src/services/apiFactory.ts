/**
 * API Factory - Dependency Injection Container
 *
 * Provides a clean factory pattern for creating API clients with proper dependency injection.
 * Follows SOLID principles and allows for easy testing and configuration management.
 */

import config, { type EnvConfig } from '@/config';
import { ApiClient } from '@/api/api';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';
import { apiConfigService } from './apiConfigService';
import type { IStorageRepository, IApiConfigService, ApiClientOptions } from '@/types';

/**
 * API Factory class for dependency injection
 */
export class ApiFactory {
	private static instance: ApiClient | null = null;

	/**
	 * Create a new API client with injected dependencies
	 */
	static createApiClient(
		envConfig: EnvConfig = config,
		storageRepository: IStorageRepository = secureStorageRepository,
		configService: IApiConfigService = apiConfigService,
		options: ApiClientOptions = {},
	): ApiClient {
		return new ApiClient(envConfig, storageRepository, configService, options);
	}

	/**
	 * Get or create singleton API client instance
	 * Use this for most application needs
	 */
	static getApiClient(
		_env: string = config.ENV_NAME,
		envConfig: EnvConfig = config,
		storageRepository: IStorageRepository = secureStorageRepository,
		configService: IApiConfigService = apiConfigService,
		options: ApiClientOptions = {},
	): ApiClient {
		if (!this.instance) {
			this.instance = this.createApiClient(
				envConfig,
				storageRepository,
				configService,
				options,
			);
		}
		return this.instance;
	}

	/**
	 * Reset singleton instance (useful for testing)
	 */
	static resetInstance(): void {
		this.instance = null;
	}

	/**
	 * Create API client for testing with mock dependencies
	 */
	static createTestApiClient(
		mockConfig: Partial<EnvConfig> = {},
		mockStorage: IStorageRepository,
		mockConfigService: IApiConfigService,
		options: ApiClientOptions = {},
	): ApiClient {
		const testConfig: EnvConfig = {
			ENV_NAME: 'test',
			API_URL: 'https://test-api.example.com',
			DEBUG: true,
			AUTH_REDIRECT_URL: 'learningcoachcommunity://auth/success',
			...mockConfig,
		};

		return this.createApiClient(testConfig, mockStorage, mockConfigService, options);
	}
}

/**
 * Default singleton instance - use this for most application needs
 */
// Note: No default or singleton exports to keep API surface minimal.
