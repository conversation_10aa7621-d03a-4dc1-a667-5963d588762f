import CustomButton from '@/components/ui/CustomButton';
import { ThemedView } from '@/components/ThemedView';
import { useTheme, Tokens } from '@/theme';
import { StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';

import { JellyIcon } from '@/components/ui/JellyIcon';

export default function PostComposerButton() {
	const theme = useTheme();
	const router = useRouter();
	return (
		<ThemedView style={styles.container}>
			<CustomButton
				variant='round'
				onPress={() => {
					router.push('/social/post-composer');
				}}
			>
				<JellyIcon
					name='envelope'
					variant='fill'
					color={theme.colors.primary}
					size={Tokens.Sizes.icon}
				/>
			</CustomButton>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		alignItems: 'flex-end',
		width: '100%',
		backgroundColor: 'transparent',
	},
});
