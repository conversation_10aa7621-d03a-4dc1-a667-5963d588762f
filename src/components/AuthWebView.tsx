import React, { useRef } from 'react';
import { View, ActivityIndicator, Text, StyleSheet, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import { authConfigUtils } from '@/utils/authConfig';

interface AuthWebViewProps {
	loginUrl: string;
	onDeepLink: (url: string) => void;
	onError?: (message: string) => void;
	visible?: boolean;
	theme: { colors: { background: string; text: string; primary: string } };
}

/**
 * AuthWebView
 *
 * Encapsulates the WebView-based authentication flow:
 * - Loads the middleware login URL constructed by AuthService.getLoginUrl()
 * - Intercepts deep links using schemes derived from config.AUTH_REDIRECT_URL
 * - As a fallback, extracts the deep link from the callback page content (standard path '/sso-auth/callback')
 * - Uses i18n for UI strings (e.g., 'auth.loadingAuth')
 *
 * Configuration is loaded from environment-specific config files:
 * - Single source of truth: AUTH_REDIRECT_URL (e.g., "exp://auth-callback" or "learningcoachcommunity://auth-callback")
 */
export const AuthWebView: React.FC<AuthWebViewProps> = ({
	loginUrl,
	onDeepLink,
	onError,
	visible = true,
	theme,
}) => {
	const webviewRef = useRef<WebView>(null);

	if (!visible) {
		return null;
	}

	const handleWebViewError = (error: any) => {
		const errorMessage = error?.nativeEvent?.description || 'WebView error occurred';
		onError?.(errorMessage);
	};

	const handleWebViewMessage = (event: any) => {
		try {
			const message = JSON.parse(event.nativeEvent.data);
			if (message.type === 'DEEP_LINK_FOUND' && message.url) {
				// Validate the deep link using our config utilities
				if (authConfigUtils.isDeepLinkUrl(message.url)) {
					onDeepLink(message.url);
				}
			}
		} catch {}
	};

	const handleNavigationStateChange = (navState: any) => {
		// When the callback URL finishes loading, check for deep link on the page
		if (authConfigUtils.isCallbackUrl(navState.url) && !navState.loading) {
			// Build regex pattern from configured schemes for JavaScript injection
			const schemes = authConfigUtils.getSupportedSchemes();
			const schemePatterns = schemes.map(scheme => {
				// Escape special regex characters for JavaScript
				const escapedScheme = scheme.replace(/[.*+?^${}()|[\]\\]/g, '\\\\$&');
				return `${escapedScheme}[^\\\\s]*?auth-callback\\\\?[^\\\\s]+`;
			});
			const regexPattern = `(${schemePatterns.join('|')})`;

			const jsCode = `
        (function() {
          try {
            const bodyText = document.body ? document.body.innerText || document.body.textContent : '';
            const preText = document.querySelector('pre') ? document.querySelector('pre').innerText : '';
            const allText = bodyText + ' ' + preText;
            const deepLinkMatch = allText.match(/${regexPattern}/);
            if (deepLinkMatch) {
              window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'DEEP_LINK_FOUND', url: deepLinkMatch[1] }));
            }
          } catch (e) {
            window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'ERROR', error: e.message }));
          }
        })();
        true;
      `;
			webviewRef.current?.injectJavaScript(jsCode);
		}
	};

	const handleShouldStartLoad = (request: any) => {
		// Intercept deep link URLs and handle them as deep links instead of navigation
		if (request.url && authConfigUtils.isDeepLinkUrl(request.url)) {
			onDeepLink(request.url);
			return false; // Prevent WebView from navigating to the deep link
		}
		return true;
	};

	const isIOS = Platform.OS === 'ios';

	if (isIOS && __DEV__) {
		console.log('iOS WebView config with cookie handling');
	}

	return (
		<WebView
			ref={webviewRef}
			source={{ uri: loginUrl }}
			onShouldStartLoadWithRequest={handleShouldStartLoad}
			onNavigationStateChange={handleNavigationStateChange}
			onError={handleWebViewError}
			onMessage={handleWebViewMessage}
			startInLoadingState
			javaScriptEnabled
			domStorageEnabled
			sharedCookiesEnabled={isIOS} // Enable shared cookies for iOS
			thirdPartyCookiesEnabled={true} // Enable third-party cookies
			cacheEnabled={true}
			incognito={false} // Make sure we're not in incognito mode
			renderLoading={() => (
				<View
					style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}
				>
					<ActivityIndicator size='large' color={theme.colors.primary} />
					<Text style={[styles.loadingText, { color: theme.colors.text }]}>
						{authConfigUtils.getLoadingText()}
					</Text>
				</View>
			)}
		/>
	);
};

const styles = StyleSheet.create({
	loadingContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 20,
	},
	loadingText: {
		marginTop: 16,
		fontSize: 16,
		textAlign: 'center',
	},
});
