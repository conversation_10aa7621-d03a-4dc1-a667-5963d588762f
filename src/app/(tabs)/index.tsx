import { Image } from 'expo-image';
import { StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { selectUser } from '@/features/user/userSlice';
import { Tokens } from '@/theme';
import { PostsTimeline, PostComposerButton } from '@/features/posts';
export default function HomeScreen() {
	const user = useSelector(selectUser);

	return (
		<ParallaxScrollView
			headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
			headerImage={
				<Image source={require('@assets/images/logo.svg')} style={styles.reactLogo} />
			}
			overlay={<PostComposerButton />}
		>
			<ThemedView style={styles.greetingContainer}>
				<ThemedText type='defaultSemiBold'>
					Hello {user?.email ?? 'Guest'}
				</ThemedText>
				{user && (
					<ThemedText type='default' style={styles.userInfo}>
						Account ID: {user.una_account_id} | Role: {user.role} | Status:
						{user.status}
					</ThemedText>
				)}
			</ThemedView>
			<PostsTimeline />
		</ParallaxScrollView>
	);
}

const styles = StyleSheet.create({
	greetingContainer: {
		marginBottom: Tokens.Spacing.m,
		padding: Tokens.Spacing.m,
		borderRadius: Tokens.Radius.s,
		backgroundColor: 'rgba(161, 206, 220, 0.1)',
	},
	userInfo: {
		marginBottom: Tokens.Spacing.s,
		opacity: 0.8,
	},
	titleContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		gap: Tokens.Spacing.s,
	},
	stepContainer: {
		gap: Tokens.Spacing.s,
		marginBottom: Tokens.Spacing.s,
	},
	reactLogo: {
		height: 48,
		width: 96,
		top: Tokens.Spacing.l,
		left: Tokens.Spacing.m,
		position: 'absolute',
	},
});
