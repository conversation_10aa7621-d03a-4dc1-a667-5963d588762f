
definitions:
  steps:
    - step: &build-step
        name: SonarQube analysis
        services:
          - docker
        memory: 4096
        size: 4x
        max-time: 1
        script:               
          - pipe: sonarsource/sonarqube-scan:3.0.2
            variables:
              SONAR_HOST_URL: ${SONAR_HOST_URL} # Get the value from the repository/workspace variable.             
              SONAR_TOKEN: ${SONAR_TOKEN} # Get the value from the repository/workspace variable. You shouldn't set secret in clear text here.
              EXTRA_ARGS: -Dsonar.scanner.skipJreProvisioning=true
  caches:
    sonar: ~/.sonar

clone:
  depth: full

pipelines:
  branches:
    '{dev}':
      - step: *build-step    