import React, { useEffect } from 'react';
import { FlatList, StyleSheet } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useSelector, useDispatch } from 'react-redux';
import { PostAuthor } from './PostAuthor';
import type { AppDispatch } from '@/store';
import {
	fetchPosts,
	selectPosts,
	selectIsLoading,
	selectIsInitialized,
	selectError,
} from '@/features/posts/postsSlice';
import { LoadingIndicator } from '@/components/LoadingIndicator';

export default function PostsTimeline() {
	const posts = useSelector(selectPosts);
	const isLoading = useSelector(selectIsLoading);
	const isInitialized = useSelector(selectIsInitialized);
	const error = useSelector(selectError);
	const dispatch = useDispatch<AppDispatch>();

	useEffect(() => {
		if (!isInitialized && !isLoading) {
			dispatch(fetchPosts());
		}
	}, [isInitialized, isLoading, dispatch]);

	if (isLoading) {
		return (
			<ThemedView style={styles.container}>
				<LoadingIndicator />
			</ThemedView>
		);
	}

	if (error) {
		return (
			<ThemedView style={styles.container}>
				<ThemedText type='error'>Error: {error}</ThemedText>
			</ThemedView>
		);
	}

	return (
		<ThemedView style={styles.container}>
			<FlatList
				data={posts}
				keyExtractor={(item) => String(item.id)}
				renderItem={({ item }) => (
					<ThemedView style={styles.post}>
						<PostAuthor author={item.author_data} />
						<ThemedText type='subtitle'>{item.content.title}</ThemedText>
						<ThemedText>{item.content.text}</ThemedText>
						<ThemedText>{new Date(item.date).toLocaleString()}</ThemedText>
					</ThemedView>
				)}
				contentContainerStyle={styles.listContent}
			/>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
	},
	listContent: {
		paddingBottom: 80, // Space for the button
	},
	post: {
		padding: 16,
		marginBottom: 12,
		backgroundColor: '#eee',
		borderRadius: 8,
		maxWidth: 500,
		minWidth: '100%',
	},
});
