/**
 * Map of Font Awesome Jelly Icon names to Unicode codepoints
 * View the full icon set at https://fontawesome.com/icons?d=gallery&s=solid&m=jelly
 */

export type GlyphMap = Record<string, number>;

export const JELLY_GLYPHS: GlyphMap = {
	addressCard: 0xf2bb,
	alarmClock: 0xf34e,
	anchor: 0xf13d,
	angleDown: 0xf107,
	angleLeft: 0xf104,
	angleRight: 0xf105,
	angleUp: 0xf106,
	arrowDown: 0xf063,
	arrowDownToLine: 0xf33d,
	arrowDownWideShort: 0xf160,
	arrowLeft: 0xf060,
	arrowRight: 0xf061,
	arrowRightArrowLeft: 0xf0ec,
	arrowRightFromBracket: 0xf08b,
	arrowRightToBracket: 0xf090,
	arrowRotateLeft: 0xf0e2,
	arrowRotateRight: 0xf01e,
	arrowsRotate: 0xf021,
	arrowUp: 0xf062,
	arrowUpFromBracket: 0xe09a,
	arrowUpFromLine: 0xf342,
	arrowUpRightFromSquare: 0xf08e,
	arrowUpWideShort: 0xf161,
	at: 0x0040,
	backward: 0xf04a,
	backwardStep: 0xf048,
	bagShopping: 0xf290,
	bars: 0xf0c9,
	batteryBolt: 0xf376,
	batteryEmpty: 0xf244,
	batteryHalf: 0xf242,
	batteryLow: 0xe0b1,
	batteryThreeQuarters: 0xf241,
	bed: 0xf236,
	bell: 0xf0f3,
	blockQuote: 0xe0b5,
	bold: 0xf032,
	bolt: 0xf0e7,
	bomb: 0xf1e2,
	book: 0xf02d,
	bookmark: 0xf02e,
	bookOpen: 0xf518,
	box: 0xf466,
	boxArchive: 0xf187,
	bug: 0xf188,
	building: 0xf1ad,
	bus: 0xf207,
	cakeCandles: 0xf1fd,
	calendar: 0xf133,
	camera: 0xf030,
	cameraSlash: 0xe0d9,
	car: 0xf1b9,
	cartShopping: 0xf07a,
	chartBar: 0xf080,
	chartPie: 0xf200,
	check: 0xf00c,
	circle: 0xf111,
	circleCheck: 0xf058,
	circleHalfStroke: 0xf042,
	circleInfo: 0xf05a,
	circlePlus: 0xf055,
	circleQuestion: 0xf059,
	circleUser: 0xf2bd,
	circleXmark: 0xf057,
	city: 0xf64f,
	clipboard: 0xf328,
	clock: 0xf017,
	clone: 0xf24d,
	cloud: 0xf0c2,
	code: 0xf121,
	command: 0xe142,
	comment: 0xf075,
	commentDots: 0xf4ad,
	comments: 0xf086,
	compactDisc: 0xf51f,
	compass: 0xf14e,
	compress: 0xf066,
	creditCard: 0xf09d,
	crown: 0xf521,
	database: 0xf1c0,
	desktop: 0xf390,
	doorClosed: 0xf52a,
	droplet: 0xf043,
	ellipsis: 0xf141,
	envelope: 0xf0e0,
	equals: 0x003d,
	expand: 0xf065,
	eye: 0xf06e,
	eyeSlash: 0xf070,
	faceFrown: 0xf119,
	faceGrin: 0xf580,
	faceMeh: 0xf11a,
	faceSmile: 0xf118,
	file: 0xf15b,
	files: 0xe178,
	film: 0xf008,
	filter: 0xf0b0,
	fire: 0xf06d,
	fish: 0xf578,
	flag: 0xf024,
	flower: 0xf7ff,
	folder: 0xf07b,
	folders: 0xf660,
	font: 0xf031,
	fontAwesome: 0xf2b4,
	fontCase: 0xf866,
	forward: 0xf04e,
	forwardStep: 0xf051,
	gamepad: 0xf11b,
	gauge: 0xf624,
	gear: 0xf013,
	gift: 0xf06b,
	globe: 0xf0ac,
	grid: 0xe195,
	hand: 0xf256,
	headphones: 0xf025,
	heart: 0xf004,
	heartHalf: 0xe1ab,
	hourglass: 0xf254,
	house: 0xf015,
	image: 0xf03e,
	images: 0xf302,
	inbox: 0xf01c,
	italic: 0xf033,
	key: 0xf084,
	landmark: 0xf66f,
	language: 0xf1ab,
	laptop: 0xf109,
	layerGroup: 0xf5fd,
	leaf: 0xf06c,
	lifeRing: 0xf1cd,
	lightbulb: 0xf0eb,
	link: 0xf0c1,
	list: 0xf03a,
	listOl: 0xf0cb,
	locationArrow: 0xf124,
	locationDot: 0xf3c5,
	lock: 0xf023,
	lockOpen: 0xf3c1,
	magnifyingGlass: 0xf002,
	magnifyingGlassMinus: 0xf010,
	magnifyingGlassPlus: 0xf00e,
	map: 0xf279,
	martiniGlass: 0xf57b,
	microphone: 0xf130,
	microphoneSlash: 0xf131,
	minus: 0xf068,
	mobile: 0xf3ce,
	moneyBill: 0xf0d6,
	moon: 0xf186,
	mugHot: 0xf7b6,
	music: 0xf001,
	newspaper: 0xf1ea,
	notdef: 0xe1fe,
	palette: 0xf53f,
	paperclip: 0xf0c6,
	paperPlane: 0xf1d8,
	pause: 0xf04c,
	paw: 0xf1b0,
	pencil: 0xf303,
	percent: 0x0025,
	personBiking: 0xf84a,
	phone: 0xf095,
	phoneSlash: 0xf3dd,
	plane: 0xf072,
	play: 0xf04b,
	playPause: 0xe22f,
	plus: 0x002b,
	print: 0xf02f,
	question: 0x003f,
	quoteLeft: 0xf10d,
	rectangle: 0xf2fa,
	rectangleTall: 0xe791,
	rectangleWide: 0xf2fc,
	retangleVertical: 0xf2fb,
	shareNodes: 0xf1e0,
	shield: 0xf132,
	shieldHalved: 0xf3ed,
	ship: 0xf21a,
	shirt: 0xf553,
	shop: 0xf54f,
	sidebar: 0xe24e,
	sidebarFlip: 0xe24f,
	signalBars: 0xf690,
	signalBarsFair: 0xf692,
	signalBarsGood: 0xf693,
	signalBarsSlash: 0xf694,
	signalBarsWeak: 0xf691,
	sissors: 0xf0c4,
	skull: 0xf54c,
	sliders: 0xf1de,
	snowflake: 0xf2dc,
	sort: 0xf0dc,
	sparkles: 0xf890,
	square: 0xf0c8,
	squareCode: 0xe267,
	star: 0xf005,
	starHalf: 0xf089,
	stop: 0xf04d,
	stopwatch: 0xf2f2,
	strikethrough: 0xf0cc,
	suitcase: 0xf0f2,
	sun: 0xf185,
	tag: 0xf02b,
	terminal: 0xf120,
	thumbsDown: 0xf165,
	thumbsUp: 0xf164,
	thumbtack: 0xf08d,
	ticket: 0xf145,
	train: 0xf238,
	trash: 0xf1f8,
	tree: 0xf1bb,
	triangle: 0xf2ec,
	triangleExclamation: 0xf071,
	trophy: 0xf091,
	truck: 0xf0d1,
	tvRetro: 0xf401,
	umbrella: 0xf0e9,
	universalAccess: 0xf29a,
	user: 0xf007,
	users: 0xf0c0,
	utensils: 0xf2e7,
	video: 0xf03d,
	videoSlash: 0xf4e2,
	volume: 0xf6a8,
	volumeLow: 0xf027,
	volumeOff: 0xf026,
	volumeXmark: 0xf6a9,
	volumnSlash: 0xf2e2,
	wandMagicSparkles: 0xe2ca,
	wheelchairMove: 0xe2ce,
	wifi: 0xf1eb,
	wifiFair: 0xf6ab,
	wifiSlash: 0xf6ac,
	wifiWeak: 0xf6aa,
	wrench: 0xf0ad,
	xmark: 0xf00d,
};
