/**
 * Auth Configuration Utilities
 * 
 * Provides utilities for handling authentication configuration,
 * deep link validation, and auth-related URL patterns.
 */

import config from '@/config';
import i18n from '@/translations';

export interface AuthConfigUtils {
  isDeepLinkUrl: (url: string) => boolean;
  isCallbackUrl: (url: string) => boolean;
  extractDeepLinkFromText: (text: string) => string | null;
  getLoadingText: () => string;
  getSupportedSchemes: () => string[];
  getCallbackPath: () => string;
}

// Single source of truth: AUTH_REDIRECT_URL
// Derive deep link schemes from the configured redirect URL.
const getSchemesFromRedirect = (): string[] => {
  try {
    const u = new URL(config.AUTH_REDIRECT_URL);
    // URL.protocol includes trailing ':'
    const scheme = u.protocol.replace(':', '');
    return [`${scheme}://`];
  } catch {
    // If parsing fails, return empty to avoid accidental matches; caller should handle gracefully
    return [];
  }
};

// Callback path on the middleware (standard, non-API path)
const CALLBACK_PATH = '/sso-auth/callback';

/**
 * Check if a URL matches any of the configured deep link schemes
 */
export const isDeepLinkUrl = (url: string): boolean => {
  if (!url) return false;
  const schemes = getSchemesFromRedirect();
  return schemes.some(scheme => {
    const lowerUrl = url.toLowerCase();
    return lowerUrl.startsWith(scheme.toLowerCase()) && lowerUrl.includes('auth-callback');
  });
};

/**
 * Check if a URL is the auth callback URL
 */
export const isCallbackUrl = (url: string): boolean => {
  if (!url) return false;
  return url.includes(CALLBACK_PATH);
};

/**
 * Extract deep link URL from text content using configured schemes
 */
export const extractDeepLinkFromText = (text: string): string | null => {
  if (!text) return null;
  const schemes = getSchemesFromRedirect();
  if (!schemes.length) return null;
  const schemePatterns = schemes.map(scheme => {
    const escapedScheme = scheme.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    return `${escapedScheme}[^\\s]*?auth-callback\\?[^\\s]+`;
  });
  const pattern = `(${schemePatterns.join('|')})`;
  const regex = new RegExp(pattern);
  const match = text.match(regex);
  return match ? match[1] : null;
};

/**
 * Get the configured loading text
 */
export const getLoadingText = (): string => {
  return i18n.t('auth.loadingAuth');
};

/**
 * Get the list of supported deep link schemes
 */
export const getSupportedSchemes = (): string[] => {
  return getSchemesFromRedirect();
};

/**
 * Get the configured callback path
 */
export const getCallbackPath = (): string => {
  return CALLBACK_PATH;
};

/**
 * Auth configuration utilities object
 */
export const authConfigUtils: AuthConfigUtils = {
  isDeepLinkUrl,
  isCallbackUrl,
  extractDeepLinkFromText,
  getLoadingText,
  getSupportedSchemes,
  getCallbackPath,
};

/**
 * Default export for convenience
 */
export default authConfigUtils;
