/**
 * AuthScreen Component
 *
 * Handles WebView-based SSO authentication flow.
 * Navigation is handled by router guards based on Redux auth state.
 * This component focuses solely on authentication logic.
 */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';
import { useTheme } from '@/theme';
import { AuthService, createAuthService } from '@/services/authService';
import { showAlertWithCallback } from '@/utils/alert';
import { useDispatch } from 'react-redux';
import type { AppDispatch } from '@/store';
import { fetchUser } from '@/features/user/userSlice';
import i18n from '@/translations';

interface AuthScreenProps {
	onAuthSuccess?: (result: { token: string; user?: any }) => void;
	onAuthError?: (error: string) => void;
}

// AuthScreen now focuses solely on authentication - router handles navigation

export default function AuthScreen({ onAuthSuccess, onAuthError }: AuthScreenProps) {
	const [loading, setLoading] = useState(false);
	const [authService, setAuthService] = useState<AuthService | null>(null);
	const [processingAuth, setProcessingAuth] = useState(false);
	const [stateParam, setStateParam] = useState<string | null>(null);
	const webviewRef = useRef<WebView>(null);
	const theme = useTheme();
	const dispatch = useDispatch<AppDispatch>();

	useEffect(() => {
		// Initialize auth service
		(async () => {
			const svc = await createAuthService();
			setAuthService(svc);
			// Generate state for this auth attempt using the service
			const st = svc.generateState(16);
			setStateParam(st);

			// Check if already authenticated - but don't navigate
			// Let the router guards handle the navigation
			const existing = await svc.getToken();
			if (existing) {
				if (__DEV__) console.log('[AuthScreen] Token already present');
				// Trigger user fetch to update Redux state
				try {
					await dispatch(fetchUser()).unwrap();
				} catch (error: any) {
					if (__DEV__) {
						console.warn('[AuthScreen] Failed to fetch user:', error);

						// If the error is about invalid/expired token, clear the token
						if (
							error?.message?.includes('token invalid') ||
							error?.message?.includes('token expired') ||
							error?.message?.includes('401')
						) {
							console.warn(
								'[AuthScreen] Invalid token detected at startup - will proceed with auth flow',
							);
							// Don't return early - let the auth flow continue with the webview
						} else {
							// For other errors, still continue with auth flow
							console.warn(
								'[AuthScreen] Error fetching user profile - will proceed with auth flow',
							);
						}
					}
					// Even with a token error, we continue to the auth flow below
					// Don't return early
				}
			}
			// Router will redirect based on auth state after this function completes
		})();
	}, [dispatch]);

	// Derive allowed origin when service is ready
	const allowedOrigin = useMemo(() => {
		try {
			return authService?.getAllowedOrigin() ?? '';
		} catch {
			return '';
		}
	}, [authService]);

	const handleWebViewError = (error: any) => {
		const errorMessage = error?.nativeEvent?.description || 'WebView error occurred';
		if (__DEV__) console.error('[AuthScreen] WebView error:', error);
		onAuthError?.(errorMessage);
		showAlertWithCallback(
			i18n.t('auth.connectionErrorTitle'),
			i18n.t('auth.connectionErrorMessage'),
		);
	};

	const handleWebViewMessage = async (event: any) => {
		try {
			const originUrl: string | undefined = event?.nativeEvent?.url;
			const payloadStr: string = event?.nativeEvent?.data ?? '';
			if (!payloadStr) return;
			const payload = JSON.parse(payloadStr);
			if (__DEV__) console.log('[AuthScreen] WebView message:', payload);

			// Extract origins
			const eventOrigin = (() => {
				try {
					return originUrl ? new URL(originUrl).origin : '';
				} catch {
					return '';
				}
			})();
			const payloadOrigin: string | undefined = payload?.origin;

			// Primary origin check: payload.origin must match the page origin we received the event from
			if (!eventOrigin || !payloadOrigin || payloadOrigin !== eventOrigin) {
				if (__DEV__)
					console.warn('[AuthScreen] Origin mismatch', {
						eventOrigin,
						payloadOrigin,
						originUrl,
						allowedOrigin,
					});
				return;
			}

			// Secondary (soft) check: warn if event/page origin doesn't start with allowedOrigin (but don't block)
			if (allowedOrigin && !eventOrigin.startsWith(allowedOrigin)) {
				if (__DEV__)
					console.warn(
						'[AuthScreen] Event origin differs from allowedOrigin (soft warning)',
						{ eventOrigin, allowedOrigin },
					);
			}

			// Schema validation
			if (!payload || payload.type !== 'AUTH_RESULT' || payload.version !== 1) return;
			if (!payload.result || !payload.result.status) return;

			// Strict state validation: reject mismatched state to prevent CSRF
			if (stateParam && payload.state && payload.state !== stateParam) {
				if (__DEV__)
					console.warn('[AuthScreen] State mismatch', {
						localState: stateParam,
						receivedState: payload.state,
					});
				showAlertWithCallback(
					i18n.t('auth.errorTitle'),
					i18n.t('auth.stateMismatchMessage'),
				);
				return; // Abort auth processing on state mismatch
			}

			if (payload.result.status === 'success') {
				const shortToken: string | undefined = payload.result.shortToken;
				if (!shortToken) {
					if (__DEV__) console.error('[AuthScreen] success without shortToken');
					return;
				}

				setProcessingAuth(true);
				setLoading(true);

				const exchange = await authService!.exchangeToken(shortToken);
				if (!exchange.success || !exchange.token) {
					const errorMessage = exchange.error || 'Token exchange failed';
					if (__DEV__) console.error('[AuthScreen] Token exchange failed:', errorMessage);
					onAuthError?.(errorMessage);
					showAlertWithCallback(i18n.t('auth.failedTitle'), errorMessage);
					setProcessingAuth(false);
					setLoading(false);
					return;
				}

				// Update Redux state - let router handle navigation
				try {
					await dispatch(fetchUser()).unwrap();
					if (onAuthSuccess) {
						onAuthSuccess({ token: exchange.token });
					}
					// Router will automatically redirect to protected routes
				} catch (error) {
					if (__DEV__)
						console.warn('[AuthScreen] Failed to fetch user after auth:', error);
					onAuthError?.(i18n.t('auth.failedToLoadProfile'));
				} finally {
					setProcessingAuth(false);
					setLoading(false);
				}
			} else if (payload.result.status === 'error') {
				const code = payload.result.error?.code || 'auth_error';
				const msg = payload.result.error?.message || 'Authentication failed';
				if (__DEV__) console.error('[AuthScreen] Auth error:', code, msg);
				onAuthError?.(msg);
				showAlertWithCallback(i18n.t('auth.errorTitle'), msg);
			} else if (payload.result.status === 'cancel') {
				if (__DEV__) console.log('[AuthScreen] Auth cancelled by user');
				// Don't navigate - let user stay on auth screen or use back button
			}
		} catch (error) {
			if (__DEV__) console.error('[AuthScreen] Failed to process WebView message:', error);
			showAlertWithCallback(
				i18n.t('auth.errorTitle'),
				i18n.t('auth.unexpectedError'),
			);
		}
	};

	const handleNavigationStateChange = (navState: any) => {
		if (__DEV__) {
			console.log('=== Navigation State Change ===');
			console.log('URL:', navState.url);
			console.log('Loading:', navState.loading);
			console.log('===============================');
		}
	};

	const handleShouldStartLoad = (request: any) => {
		if (__DEV__) {
			console.log('=== Should Start Load ===');
			console.log('URL:', request.url);
			console.log('Method:', request.method);
			console.log('========================');
		}
		const url: string = request?.url ?? '';
		if (!url) return false;
		// Block data URLs and javascript:
		if (url.startsWith('data:') || url.startsWith('javascript:')) return false;
		// Allow normal web navigations (middleware domain, Okta, etc.)
		if (url.startsWith('http://') || url.startsWith('https://')) return true;
		// Block any custom schemes from navigating inside the WebView
		return false;
	};

	if (!authService) {
		return (
			<View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
				<ActivityIndicator size='large' color={theme.colors.primary} />
				<Text style={[styles.loadingText, { color: theme.colors.text }]}>
					{i18n.t('auth.initializing')}
				</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			{(loading || processingAuth) && (
				<View style={[styles.overlay, { backgroundColor: theme.colors.background }]}>
					<ActivityIndicator size='large' color={theme.colors.primary} />
					<Text style={[styles.loadingText, { color: theme.colors.text }]}>
						{processingAuth ? i18n.t('auth.completing') : i18n.t('common.loading')}
					</Text>
				</View>
			)}
			{!processingAuth && (
				<WebView
					ref={webviewRef}
					source={{ uri: authService.getLoginUrl(stateParam || undefined) }}
					onShouldStartLoadWithRequest={handleShouldStartLoad}
					onNavigationStateChange={handleNavigationStateChange}
					onError={handleWebViewError}
					onMessage={handleWebViewMessage}
					onLoadStart={(e) => {
						if (__DEV__) console.log('[AuthScreen] onLoadStart:', e.nativeEvent.url);
					}}
					onLoadEnd={(e) => {
						if (__DEV__) console.log('[AuthScreen] onLoadEnd:', e.nativeEvent.url);
					}}
					onHttpError={(e) => {
						if (__DEV__)
							console.error(
								'[AuthScreen] onHttpError:',
								e.nativeEvent.statusCode,
								e.nativeEvent.description,
								e.nativeEvent.url,
							);
						showAlertWithCallback(
							i18n.t('auth.connectionErrorTitle'),
							i18n.t('auth.connectionErrorMessage'),
						);
					}}
					startInLoadingState
					javaScriptEnabled
					domStorageEnabled
					originWhitelist={['http://*', 'https://*']}
					mixedContentMode='always'
					thirdPartyCookiesEnabled
					sharedCookiesEnabled
					setSupportMultipleWindows
					renderLoading={() => (
						<View
							style={[
								styles.loadingContainer,
								{ backgroundColor: theme.colors.background },
							]}
						>
							<ActivityIndicator size='large' color={theme.colors.primary} />
							<Text style={[styles.loadingText, { color: theme.colors.text }]}>
								{i18n.t('auth.loadingAuth')}
							</Text>
						</View>
					)}
				/>
			)}
		</View>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
	loadingContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 20,
	},
	loadingText: {
		marginTop: 16,
		fontSize: 16,
		textAlign: 'center',
	},
	overlay: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		bottom: 0,
		justifyContent: 'center',
		alignItems: 'center',
		zIndex: 1000,
		opacity: 0.9,
	},
});
