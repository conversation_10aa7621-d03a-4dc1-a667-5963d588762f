import { buildRouteUrl, type RouteDef } from '@/api/routes';
import { ApiMethods } from '@/types';

describe('buildRouteUrl', () => {
	test('normalizes base URL without double slashes', () => {
		const baseWithSlash = 'https://api.example.com/';
		const baseWithoutSlash = 'https://api.example.com';

		const route: RouteDef = { path: '/auth/login', method: ApiMethods.GET } as any;

		expect(buildRouteUrl(baseWithSlash, route)).toBe('https://api.example.com/auth/login');
		expect(buildRouteUrl(baseWithoutSlash, route)).toBe('https://api.example.com/auth/login');
	});

	test('encodes query parameters correctly (spaces and special chars)', () => {
		const base = 'https://api.example.com/';
		const route: RouteDef = {
			path: '/auth/login',
			method: ApiMethods.GET,
			authenticated: false,
			queryParams: {
				platform: 'mobile',
				deep_link_base: 'exp://127.0.0.1:8081/--/auth-callback?param=hello world &x=1+1',
			},
		} as any;

		const url = buildRouteUrl(base, route);
		// Ensure no trailing double slashes and that params are URL-encoded
		expect(url.startsWith('https://api.example.com/auth/login?')).toBe(true);
		const qs = url.split('?')[1];
		expect(qs).toBeTruthy();
		const sp = new URLSearchParams(qs);
		expect(sp.get('platform')).toBe('mobile');
		const deepLinkEncoded = sp.get('deep_link_base') || '';
		const decodedPercentOnly = decodeURIComponent(deepLinkEncoded);
		// Literal plus signs should survive percent-decoding
		expect(decodedPercentOnly).toContain('x=1+1');
		// Spaces may be represented as '+', so allow normalizing '+' to space for comparison
		expect(decodedPercentOnly.replace(/\+/g, ' ')).toContain('param=hello world ');
	});

	test('handles function route definitions', () => {
		const base = 'https://api.example.com';
		const routeFactory: RouteDef = ((platform: string, deepLinkBase?: string) => ({
			path: '/auth/login',
			method: ApiMethods.GET,
			authenticated: false,
			queryParams: { platform, ...(deepLinkBase ? { deep_link_base: deepLinkBase } : {}) },
		})) as any;

		const url = buildRouteUrl(base, routeFactory as any, 'mobile', 'exp://myhost/--');
		expect(url.startsWith('https://api.example.com/auth/login?')).toBe(true);
		const qs = url.split('?')[1];
		const sp = new URLSearchParams(qs);
		expect(sp.get('platform')).toBe('mobile');
		const deep = sp.get('deep_link_base') || '';
		expect(decodeURIComponent(deep.replace(/\+/g, ' '))).toBe('exp://myhost/--');
	});

	test('no query params returns simple path', () => {
		const base = 'https://api.example.com/';
		const route: RouteDef = { path: '/posts', method: ApiMethods.GET } as any;
		const url = buildRouteUrl(base, route);
		expect(url).toBe('https://api.example.com/posts');
	});
});
