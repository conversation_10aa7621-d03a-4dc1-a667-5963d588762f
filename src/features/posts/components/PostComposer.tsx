import { StyleSheet, Alert, TextInput } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState, AppDispatch } from '@/store';
import {
	setMessage,
	setTitle,
	submitPost,
	selectSuccess,
	closeComposer,
	selectLoading,
} from '../composerSlice';

import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/theme';
import { useEffect } from 'react';
import { fetchPosts } from '../postsSlice';
import { LoadingIndicator } from '@/components/LoadingIndicator';

interface PostComposerProps {
	onSubmit: () => void;
}

export default function PostComposer({ onSubmit }: PostComposerProps) {
	const dispatch = useDispatch<AppDispatch>();
	const { t } = useTranslation();
	const theme = useTheme();

	const title = useSelector((state: RootState) => state.composePost.title);
	const message = useSelector((state: RootState) => state.composePost.message);

	const success = useSelector(selectSuccess);
	const loading = useSelector(selectLoading);
	useEffect(() => {
		if (loading && success) {
			dispatch(closeComposer());
			dispatch(fetchPosts());
			onSubmit();
		}
	}, [success, loading, dispatch, onSubmit]);

	const onSubmitPress = () => {
		dispatch(submitPost({ title, message }));
	};

	if (loading) {
		return (
			<ThemedView style={styles.centered}>
				<LoadingIndicator />
			</ThemedView>
		);
	}

	return (
		<ThemedView style={styles.container}>
			<TextInput
				style={[styles.title, { borderColor: theme.colors.border }]}
				placeholder={t('social.postComposer.placeholder')}
				value={title}
				onChangeText={(text) => dispatch(setTitle(text))}
				autoFocus
				returnKeyType='next'
			/>
			<TextInput
				style={[styles.input, { borderColor: theme.colors.border }]}
				placeholder={t('social.postComposer.placeholder')}
				value={message}
				onChangeText={(text) => dispatch(setMessage(text))}
				multiline
				autoFocus
				returnKeyType='send'
				onSubmitEditing={() => Alert.alert('Post submitted!')}
			/>
			<CustomButton style={styles.submitButton} variant='primary' onPress={onSubmitPress}>
				{t('social.postComposer.submit')}
			</CustomButton>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 16,
	},
	centered: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
	},
	title: {
		borderWidth: 1,
		borderRadius: 8,
		padding: 12,
		marginBottom: 16,
		minHeight: 40,
		textAlignVertical: 'top',
	},
	input: {
		borderWidth: 1,
		borderRadius: 8,
		padding: 12,
		marginBottom: 16,
		minHeight: 100,
		textAlignVertical: 'top',
	},
	submitButton: {
		marginTop: 8,
	},
});
