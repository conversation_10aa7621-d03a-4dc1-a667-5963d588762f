# Authentication Configuration

## Overview

The app uses environment-specific configuration files, and the authentication flow is implemented via a WebView that communicates completion back to the app using `postMessage`. The server manages redirects (Okta authorize and callback) and returns a small HTML page that posts a structured message back to the WebView.

## Configuration Files

### Environment-Specific Configs

- `app.config.dev.json` – Development environment configuration
- `app.config.prod.json` – Production environment configuration  
- `app.config.json` – Default/fallback configuration

### Configuration Loading

The configuration is automatically loaded based on the `__DEV__` flag:

```typescript
// In development (__DEV__ = true)
const config = require('../../app.config.dev.json');

// In production (__DEV__ = false)
const config = require('../../app.config.prod.json');
```

## Redirects and Completion

- The client starts authentication by opening the middleware route `/auth/login` inside a WebView. A `state` parameter is generated on the client and passed to the server to correlate the flow.
- The middleware handles redirects to the IdP (Okta) and back to its callback route, then renders a response page that posts a JSON message (with `origin` and result payload) back to the WebView.
- The app validates the message origin and state, then exchanges the short-lived token for an API token and proceeds.

Note: `AUTH_REDIRECT_URL` exists in the environment config for future alignment and tooling, but the current client flow relies on the postMessage response from the middleware rather than consuming `AUTH_REDIRECT_URL` directly.

## Benefits of This Approach

1. **Origin-checked postMessage**: Strong, simple completion signaling from middleware to app.
2. **State validation**: The app enforces strict state checks to mitigate CSRF.
3. **Environment clarity**: Config files cleanly separate environment-specific values.
4. **Flexibility**: Easy to extend for staging or other environments.

## AuthService Integration (Best Practices)

The client `AuthService` should:

- Build the middleware login URL (e.g., `/auth/login`) and pass the client-generated `state`.
- Use platform-aware base URLs (Android emulator vs iOS simulator) for local development.
- Avoid hardcoding deep link schemes; prefer the middleware’s postMessage completion.
- Perform strict origin and state validation on messages received in the WebView before exchanging tokens.
- Keep token exchange, storage, and error handling centralized (single responsibility).

Optionally, if you later decide to drive any URL patterns from config:

- Make `AUTH_REDIRECT_URL` the single source for any client-displayed links or diagnostics.
- Ensure documentation and implementation remain aligned (config → usage), and keep tests updated.

## Adding New Environments

To add a new environment (e.g., staging):

1. Create `app.config.staging.json`
2. Update the config loader in `src/config/index.ts`
3. Set appropriate build flags to distinguish staging from dev/prod
