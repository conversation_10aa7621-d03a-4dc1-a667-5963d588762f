import { useNavigation, useRouter } from 'expo-router';
import { useLayoutEffect } from 'react';
import CloseButton from '@/components/ui/CloseButton';
import { useTranslation } from 'react-i18next';
import { PostComposer } from '@/features/posts';

export default function PostComposerScreen() {
	const navigation = useNavigation();
	const router = useRouter();
	const { t, i18n } = useTranslation();

	useLayoutEffect(() => {
		navigation.setOptions({
			title: t('social.postComposer.title'),
			headerRight: () => <CloseButton />,
		});
	}, [navigation, t, i18n.language]);

	const onSubmit = () => {
		router.back();
	};

	return <PostComposer onSubmit={onSubmit} />;
}
