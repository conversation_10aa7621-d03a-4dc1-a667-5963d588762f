import { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
	useAnimatedStyle,
	useSharedValue,
	withRepeat,
	withTiming,
} from 'react-native-reanimated';

export function LoadingIndicator() {
	const rotation = useSharedValue(0);

	// Start infinite rotation
	useEffect(() => {
		rotation.value = withRepeat(withTiming(360, { duration: 1000 }), -1, false);
	}, [rotation]);

	const animatedStyle = useAnimatedStyle(() => ({
		transform: [{ rotate: `${rotation.value}deg` }],
	}));

	return (
		<Animated.View style={[styles.spinner, animatedStyle]}>
			<Animated.View style={styles.circle} />
		</Animated.View>
	);
}

const styles = StyleSheet.create({
	spinner: {
		width: 32,
		height: 32,
		justifyContent: 'center',
		alignItems: 'center',
	},
	circle: {
		width: 24,
		height: 24,
		borderWidth: 3,
		borderColor: '#888',
		borderTopColor: 'transparent',
		borderRadius: 12,
	},
});
