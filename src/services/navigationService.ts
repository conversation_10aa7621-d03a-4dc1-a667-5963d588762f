/**
 * Navigation Service
 *
 * Centralized navigation logic for post-authentication routing
 * and other complex navigation scenarios.
 */

import type { UserProfile } from '@/types/user';

export class NavigationService {
	/**
	 * Determine where to navigate after successful authentication
	 * Based on user profile and business logic
	 */
	static getPostAuthRoute(user: UserProfile | null): string {
		if (!user) {
			return '/login';
		}

		// Business logic for post-auth routing
		// This can be extended based on user properties, onboarding status, etc.

		// Example: Check if user needs onboarding
		// if (user.needsOnboarding) {
		//     return '/onboarding';
		// }

		// Example: Role-based routing
		// if (user.role === 'admin') {
		//     return '/admin';
		// }

		// Default authenticated route
		return '/(tabs)';
	}

	/**
	 * Get the default route based on authentication status
	 */
	static getDefaultRoute(isAuthenticated: boolean): string {
		return isAuthenticated ? '/(tabs)' : '/login';
	}

	/**
	 * Get the appropriate route for unauthenticated users
	 */
	static getUnauthenticatedRoute(): string {
		return '/login';
	}

	/**
	 * Get the appropriate route for authenticated users
	 */
	static getAuthenticatedRoute(): string {
		return '/(tabs)';
	}

	/**
	 * Determine if a route requires authentication
	 */
	static requiresAuth(route: string): boolean {
		const publicRoutes = ['/login', '/auth', '/'];
		return !publicRoutes.includes(route);
	}

	/**
	 * Get error route for authentication failures
	 */
	static getAuthErrorRoute(): string {
		return '/login';
	}
}
