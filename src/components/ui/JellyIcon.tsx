import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import { JELLY_GLYPHS } from '@/theme/icons';

type Variant = 'regular' | 'fill';

type IconFamilies = {
	regular: string;
	fill: string;
};

type Props = TextProps & {
	name: keyof typeof JELLY_GLYPHS;
	variant?: Variant;
	size?: number;
	color?: string;
	families?: IconFamilies;
};

const ICON_FAMILIES: IconFamilies = {
	regular: 'FontAwesome7Jelly',
	fill: 'FontAwesome7JellyFill',
};

export function JellyIcon({
	name,
	variant = 'regular',
	families = ICON_FAMILIES,
	size = 24,
	color,
	style,
	...textProps
}: Props) {
	const codepoint = JELLY_GLYPHS[String(name)];
	const fontFamily = variant === 'fill' ? families.fill : families.regular;

	return (
		<Text
			{...textProps}
			style={[
				styles.icon,
				{ fontFamily, fontSize: size, lineHeight: size * 1.2, color },
				style,
			]}
		>
			{String.fromCodePoint(codepoint)}
		</Text>
	);
}

const styles = StyleSheet.create({
	icon: {
		includeFontPadding: false,
		textAlignVertical: 'center',
	},
});
