sonar.projectKey=learningcoach_lcc_fcaf9f8c-261f-4a38-ac19-45b614e3a50e
#sonar.projectName=LCC
sonar.host.url=https://sonar.tiltedwindmillagency.com

# Exclude problematic files that cause analysis to hang
sonar.exclusions=src/app/(tabs)/dev-tools.tsx,**/__dev__/**,**/node_modules/**,**/__tests__/**,**/test/**,**/*.test.*,**/*.spec.*,**/assets/**,**/fonts/**,**/.expo/**,**/.next/**,**/build/**,**/dist/**,**/*.config.*,**/babel.config.*,**/metro.config.*,**/tailwind.config.*

# Disable TypeScript program analysis to reduce memory usage
#sonar.typescript.tsconfigPaths=
#sonar.javascript.environments=node,browser,jest
#sonar.javascript.maxFileSize=1000
#sonar.typescript.maxFileSize=1000

# Increase Node.js memory allocation for large projects
#sonar.javascript.node.maxspace=8192

# Add timeout and debugging settings
#sonar.javascript.node.timeout=600
#sonar.log.level=INFO
#sonar.verbose=false

# Disable heavy analysis features to reduce memory
# sonar.javascript.sonarlint.enabled=false
# sonar.typescript.sonarlint.enabled=false

# Skip JavaScript/TypeScript analysis entirely for now
# sonar.javascript.exclusions=**/*
# sonar.typescript.exclusions=**/*
# sonar.typescript.skipNoSonarComments=true