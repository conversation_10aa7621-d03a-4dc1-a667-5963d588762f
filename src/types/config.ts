/**
 * Configuration type definitions for the Learning Coach Community app
 */

export type Environment = 'development' | 'staging' | 'production';

export interface ApiConfig {
	baseUrl: string;
	timeout: number;
	retryAttempts: number;
}

export interface AuthConfig {
	tokenStorageKey: string;
	refreshTokenStorageKey: string;
	sessionTimeout: number; // in minutes
}

export interface AnalyticsConfig {
	enabled: boolean;
	trackingId?: string;
	debugMode: boolean;
}

export interface FeatureFlags {
	enablePushNotifications: boolean;
	enableAnalytics: boolean;
	enableOfflineMode: boolean;
	enableBetaFeatures: boolean;
	enableDebugMenu: boolean;
}

export interface AppConfig {
	// Environment
	environment: Environment;
	version: string;
	buildNumber: string;

	// API Configuration
	api: ApiConfig;

	// Authentication
	auth: AuthConfig;

	// Analytics
	analytics: AnalyticsConfig;

	// Feature Flags
	features: FeatureFlags;

	// External Services
	services: {
		firebase?: {
			apiKey: string;
			authDomain: string;
			projectId: string;
		};
		stripe?: {
			publishableKey: string;
		};
		sentry?: {
			dsn: string;
		};
	};
}

export interface ConfigValidationError {
	field: string;
	message: string;
	required: boolean;
}
