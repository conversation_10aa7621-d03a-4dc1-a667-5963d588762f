import React from 'react';
import { TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { ThemedText } from '../ThemedText';
const variantStyles = {
	default: 'rounded px-4 py-2',
	primary: 'bg-blue-600 text-white',
	secondary: 'bg-gray-200 text-black',
	round: 'rounded-full p-5 py-3 bg-white',
};

type CustomButtonProps = TouchableOpacityProps & {
	variant?: keyof typeof variantStyles;
	className?: string;
	textClassName?: string;
	children: React.ReactNode;
};

export default function CustomButton({
	variant = 'default',
	className = '',
	textClassName = '',
	children,
	...props
}: CustomButtonProps) {
	return (
		<TouchableOpacity
			className={`
        ${variantStyles.default}
        ${variantStyles[variant]}
        ${className}
      `}
			{...props}
		>
			<ThemedText type='buttonCaption'>{children}</ThemedText>
		</TouchableOpacity>
	);
}
