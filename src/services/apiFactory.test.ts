/**
 * API Factory Tests - Demonstrating proper testing with dependency injection
 */

import { ApiFactory } from './apiFactory';
import { ApiClient } from '@/api/api';
import type { IStorageRepository, IApiConfigService } from '@/types';

// Mock dependencies
class MockStorageRepository implements IStorageRepository {
	private storage = new Map<string, string>();

	async getItem(key: string): Promise<string | null> {
		return this.storage.get(key) || null;
	}

	async setItem(key: string, value: string): Promise<void> {
		this.storage.set(key, value);
	}

	async removeItem(key: string): Promise<void> {
		this.storage.delete(key);
	}

	async setObject<T>(key: string, value: T): Promise<void> {
		this.storage.set(key, JSON.stringify(value));
	}

	async getObject<T>(key: string): Promise<T | null> {
		const value = this.storage.get(key);
		return value ? JSON.parse(value) : null;
	}

	async removeObject(key: string): Promise<void> {
		this.storage.delete(key);
	}

	async hasItem(key: string): Promise<boolean> {
		return this.storage.has(key);
	}

	// Test helper methods
	setMockData(key: string, value: string) {
		this.storage.set(key, value);
	}

	clear() {
		this.storage.clear();
	}
}

class MockApiConfigService implements IApiConfigService {
	private mockConfig: { apiKey: string | null; apiBaseUrl: string | null } = {
		apiKey: 'test-api-key',
		apiBaseUrl: 'https://test-api.example.com',
	};

	async loadApiConfig() {
		return this.mockConfig;
	}

	async saveApiKey(apiKey: string): Promise<void> {
		this.mockConfig.apiKey = apiKey;
	}

	async saveApiBaseUrl(url: string): Promise<void> {
		this.mockConfig.apiBaseUrl = url;
	}

	normalizeBaseUrl(raw: string): string {
		// In tests just return the raw value
		return raw;
	}

	// Test helper methods
	setMockConfig(config: { apiKey: string | null; apiBaseUrl: string | null }) {
		this.mockConfig = config;
	}
}

describe('ApiFactory', () => {
	let mockStorage: MockStorageRepository;
	let mockConfigService: MockApiConfigService;

	beforeEach(() => {
		mockStorage = new MockStorageRepository();
		mockConfigService = new MockApiConfigService();
		ApiFactory.resetInstance(); // Reset singleton for each test
	});

	describe('createApiClient', () => {
		it('should create API client with default dependencies', () => {
			const client = ApiFactory.createApiClient();
			expect(client).toBeInstanceOf(ApiClient);
		});

		it('should create API client with custom options', () => {
			const client = ApiFactory.createApiClient(undefined, mockStorage, mockConfigService, {
				timeout: 5000,
				retryAttempts: 2,
				baseUrl: 'https://custom.example.com',
			});

			expect(client).toBeInstanceOf(ApiClient);
		});
	});

	describe('getApiClient', () => {
		it('should return singleton instance', () => {
			const client1 = ApiFactory.getApiClient();
			const client2 = ApiFactory.getApiClient();

			expect(client1).toBe(client2); // Same instance
		});

		it('should create new instance after reset', () => {
			const client1 = ApiFactory.getApiClient();
			ApiFactory.resetInstance();
			const client2 = ApiFactory.getApiClient();

			expect(client1).not.toBe(client2); // Different instances
		});
	});
});

// Integration test example
describe('ApiClient Integration', () => {
	let mockStorage: MockStorageRepository;
	let mockConfigService: MockApiConfigService;

	beforeEach(() => {
		mockStorage = new MockStorageRepository();
		mockConfigService = new MockApiConfigService();

		// Set up mock auth token
		mockStorage.setMockData('AUTH_DATA', 'mock-auth-token');
	});

	it('should use injected storage for auth headers', async () => {
		const client = ApiFactory.createTestApiClient(
			{
				API_URL: 'https://test.example.com',
				DEBUG: false,
			},
			mockStorage,
			mockConfigService,
		);

		// This would require mocking fetch, but demonstrates the concept
		// In a real test, you'd mock the fetch API and verify the Authorization header
		expect(client).toBeInstanceOf(ApiClient);
	});
});
